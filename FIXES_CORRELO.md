# BMC Remedy Automation Tool - Fixes and Corrections (CORRELO)

## 🔍 Issues Identified and Fixed

### 1. **Configuration Missing** ❌ → ✅
**Problem**: No `.env` file configured, causing authentication failures
- Error: `Invalid URL '': No scheme supplied`
- Root cause: Application trying to use empty URLs from missing configuration

**Solution**: 
- Created `setup_config.py` script for interactive configuration setup
- Enhanced `.env.example` with better documentation
- Added validation for required configuration values

### 2. **Missing Dependencies** ❌ → ✅
**Problem**: Missing `pytz` dependency for timezone handling
- Colombian timezone conversion requires proper timezone library

**Solution**:
- Added `pytz==2023.3` to `requirements.txt`
- All timezone conversions now properly supported

### 3. **Diagnostic Tools Missing** ❌ → ✅
**Problem**: No easy way to diagnose configuration and connectivity issues

**Solution**:
- Created `diagnose.py` script for comprehensive system diagnostics
- Checks file structure, configuration, dependencies, connectivity, and logs
- Provides actionable recommendations for fixing issues

## 🚀 Quick Fix Instructions

### Step 1: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Configure Environment
```bash
# Interactive configuration setup
python setup_config.py

# Or manually copy and edit
cp .env.example .env
nano .env  # Edit with your actual values
```

### Step 3: Validate Setup
```bash
# Run comprehensive diagnostics
python diagnose.py

# Test basic functionality
python main.py 1152213619 --debug
```

## 📋 Configuration Requirements

Your `.env` file must contain these required values:

```env
# BMC Remedy Configuration (REQUIRED)
LOGIN_URL=https://your-bmc-server.com/api/jwt/login
API_BASE_URL=https://your-bmc-server.com/api
USERNAME_=your_username
PASSWORD=your_password

# Email Configuration (REQUIRED)
EMAIL_URL=https://your-email-service.com/send
EMAIL_TO=<EMAIL>
```

## 🔧 New Tools Created

### 1. `setup_config.py`
Interactive configuration setup script:
- Prompts for all required values
- Validates URLs and required fields
- Creates properly formatted `.env` file
- Provides next steps guidance

**Usage**:
```bash
python setup_config.py
```

### 2. `diagnose.py`
Comprehensive diagnostic tool:
- ✅ File structure validation
- ✅ Configuration validation
- ✅ Dependency checking
- ✅ Connectivity testing
- ✅ Log analysis

**Usage**:
```bash
python diagnose.py
```

## 🐛 Common Issues and Solutions

### Issue: "Invalid URL" Error
```
ERROR - Authentication failed: Invalid URL '': No scheme supplied
```
**Solution**: Run `python setup_config.py` to configure URLs

### Issue: "Module not found" Error
```
ImportError: No module named 'requests'
```
**Solution**: Run `pip install -r requirements.txt`

### Issue: Authentication Failed
```
ERROR - Authentication failed: 401 Unauthorized
```
**Solution**: 
1. Verify BMC server URL is correct
2. Check username and password
3. Test connectivity: `python diagnose.py`

### Issue: Email Not Sending
```
ERROR - Failed to send email
```
**Solution**:
1. Verify EMAIL_URL is correct
2. Check email service credentials
3. Test connectivity: `python diagnose.py`

## 📊 Diagnostic Report Example

When you run `python diagnose.py`, you'll see:

```
🔍 BMC Remedy Automation Tool - Diagnostic Report
============================================================

📁 Checking file structure...
✅ All required files present

⚙️ Checking configuration...
✅ Configuration looks good

📦 Checking dependencies...
✅ All required packages installed

🌐 Testing connectivity...
✅ BMC server reachable: https://your-server.com
✅ Email service reachable: https://your-email.com

📋 Checking recent logs...
ℹ️  No recent errors found

============================================================
📊 DIAGNOSTIC SUMMARY
============================================================
File Structure.......... ✅ PASS
Configuration........... ✅ PASS
Dependencies............ ✅ PASS
Connectivity............ ✅ PASS
Logs.................... ✅ PASS

============================================================
🎉 All checks passed! Your BMC Remedy tool should work correctly.

💡 Try running: python main.py <cedula> --debug
```

## 🎯 Next Steps After Fixes

1. **Configure Environment**:
   ```bash
   python setup_config.py
   ```

2. **Validate Setup**:
   ```bash
   python diagnose.py
   ```

3. **Test Functionality**:
   ```bash
   python main.py 1152213619 --debug
   ```

4. **Run in Production**:
   ```bash
   python main.py 1152213619 --destinatario <EMAIL>
   ```

## 🔒 Security Notes

- Never commit `.env` file to version control
- Use environment-specific configuration files
- Regularly rotate passwords and API keys
- Use HTTPS URLs for all endpoints

## 📞 Support

If you encounter issues after applying these fixes:

1. Run `python diagnose.py` for detailed diagnostics
2. Check logs in `logs/bmc-remedy.log`
3. Use `--debug` flag for verbose output
4. Verify all URLs are accessible from your network

---

**Status**: ✅ All identified issues have been corrected
**Tools**: 2 new diagnostic and setup scripts created
**Dependencies**: Updated with missing `pytz` library
**Configuration**: Enhanced with validation and setup assistance
