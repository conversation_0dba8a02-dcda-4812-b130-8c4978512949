# Deployment Guide

## Production Deployment

### Prerequisites

- Docker 20.0+
- Docker Compose 2.0+
- Access to BMC Remedy server
- Email service endpoint

### Step-by-Step Deployment

#### 1. Environment Setup

```bash
# Create production directory
mkdir -p /opt/bmc-remedy
cd /opt/bmc-remedy

# Copy optimized project
cp -r bmc-remedy-optimized/* .

# Setup environment
cp .env.example .env
```

#### 2. Configuration

Edit `.env` file with production values:

```env
# Production BMC Configuration
LOGIN_URL=https://prod-bmc-server.company.com/api/jwt/login
API_BASE_URL=https://prod-bmc-server.company.com/api
USERNAME_=prod_service_account
PASSWORD=secure_password

# Production Email Configuration
EMAIL_URL=https://email-service.company.com/send
EMAIL_TO=<EMAIL>

# Production Settings
DEBUG=false
LOG_LEVEL=INFO
LOG_OUTPUT=file
SILENT_MODE=true
```

#### 3. Build Production Image

```bash
# Build optimized production image
docker-compose -f docker/docker-compose.yml build

# Tag for production
docker tag bmc-remedy-optimized:latest bmc-remedy-optimized:prod
```

#### 4. Security Hardening

```bash
# Set proper file permissions
chmod 600 .env
chmod -R 755 scripts/
chown -R 1000:1000 logs/

# Create dedicated network
docker network create bmc-remedy-prod
```

#### 5. Production Run

```bash
# Run with production settings
docker-compose -f docker/docker-compose.yml run --rm \
  --network bmc-remedy-prod \
  bmc-remedy python main.py <cedula>
```

### Automated Deployment Script

Create `deploy.sh`:

```bash
#!/bin/bash
set -e

# Production deployment script
DEPLOY_DIR="/opt/bmc-remedy"
BACKUP_DIR="/opt/bmc-remedy-backup"

# Backup current deployment
if [ -d "$DEPLOY_DIR" ]; then
    cp -r "$DEPLOY_DIR" "$BACKUP_DIR-$(date +%Y%m%d-%H%M%S)"
fi

# Deploy new version
cp -r bmc-remedy-optimized/* "$DEPLOY_DIR/"
cd "$DEPLOY_DIR"

# Build and test
docker-compose -f docker/docker-compose.yml build
docker-compose -f docker/docker-compose.yml --profile test run --rm bmc-remedy-test

echo "Deployment completed successfully"
```

## Monitoring and Maintenance

### Log Monitoring

```bash
# Monitor logs in real-time
tail -f logs/bmc-remedy.log

# Check log rotation
ls -la logs/

# Archive old logs
find logs/ -name "*.log.*" -mtime +30 -delete
```

### Health Checks

```bash
# Container health check
docker-compose -f docker/docker-compose.yml ps

# Application health check
docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python -c "
from src.config.settings import get_config
from src.core.bmc_client import BMCClient
config = get_config()
client = BMCClient(config.bmc_config)
print('Health check: OK')
"
```

### Backup Strategy

```bash
# Backup configuration
tar -czf backup-config-$(date +%Y%m%d).tar.gz .env logs/

# Backup logs
tar -czf backup-logs-$(date +%Y%m%d).tar.gz logs/

# Automated backup script
cat > backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/bmc-remedy"
DATE=$(date +%Y%m%d-%H%M%S)

mkdir -p "$BACKUP_DIR"
tar -czf "$BACKUP_DIR/bmc-remedy-$DATE.tar.gz" \
  .env logs/ docker/ src/

# Keep only last 30 days
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
EOF
```

## Scaling and Performance

### Resource Requirements

**Minimum Requirements:**
- CPU: 1 core
- Memory: 512MB
- Disk: 1GB

**Recommended for Production:**
- CPU: 2 cores
- Memory: 1GB
- Disk: 5GB

### Performance Tuning

```yaml
# docker-compose.override.yml for production
version: '3.8'
services:
  bmc-remedy:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### Load Balancing

For high-volume deployments:

```yaml
# docker-compose.scale.yml
version: '3.8'
services:
  bmc-remedy:
    scale: 3
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

## Security Considerations

### Network Security

```bash
# Create isolated network
docker network create --driver bridge bmc-remedy-secure

# Run with restricted network
docker-compose run --rm --network bmc-remedy-secure bmc-remedy
```

### Secrets Management

```bash
# Use Docker secrets for sensitive data
echo "secure_password" | docker secret create bmc_password -

# Update docker-compose.yml
services:
  bmc-remedy:
    secrets:
      - bmc_password
```

### SSL/TLS Configuration

```env
# Enable SSL verification in production
BMC_VERIFY_SSL=true
EMAIL_VERIFY_SSL=true
```

## Troubleshooting Production Issues

### Common Production Issues

1. **Memory Issues**
   ```bash
   # Monitor memory usage
   docker stats bmc-remedy-optimized
   
   # Increase memory limit
   docker-compose up --memory=1g
   ```

2. **Network Connectivity**
   ```bash
   # Test BMC connectivity
   docker run --rm --network host curlimages/curl \
     curl -k https://bmc-server.com/api/jwt/login
   ```

3. **Permission Issues**
   ```bash
   # Fix log permissions
   sudo chown -R 1000:1000 logs/
   sudo chmod -R 755 logs/
   ```

### Emergency Procedures

```bash
# Rollback to previous version
docker-compose down
cp -r /opt/bmc-remedy-backup-YYYYMMDD/* /opt/bmc-remedy/
docker-compose up -d

# Emergency stop
docker-compose down --remove-orphans

# Force cleanup
docker system prune -af
```
