# API Documentation

## Core Classes and Methods

### WorkflowManager

Main orchestrator for the BMC Remedy automation workflow.

```python
from core.workflow import WorkflowManager

# Initialize
workflow = WorkflowManager(config)

# Run complete workflow
success = workflow.run_complete_workflow(
    cedula="1152213619",
    destinatario="<EMAIL>",
    show_all=False
)
```

#### Methods

**`run_complete_workflow(cedula, destinatario=None, show_all=False)`**
- **Purpose**: Execute the complete automation workflow
- **Parameters**:
  - `cedula` (str): ID number to search
  - `destinatario` (str, optional): Email recipient
  - `show_all` (bool): Whether to show all incidents or limit to 5
- **Returns**: `bool` - True if successful, False otherwise

### BMCClient

Handles all BMC Remedy API interactions.

```python
from core.bmc_client import BMCClient

# Initialize
client = BMCClient(config.bmc_config)

# Search user
user_info = client.search_user_by_cedula("1152213619")

# Search tickets
tickets = client.search_tickets("1152213619", show_all=False)

# Search incidents
incidents = client.search_incidents("1152213619", show_all=False)
```

#### Methods

**`search_user_by_cedula(cedula)`**
- **Purpose**: Search for user by ID number in CTM:People
- **Parameters**: `cedula` (str) - ID number to search
- **Returns**: `Dict[str, Any]` - User information or None

**`search_tickets(cedula, show_all=False)`**
- **Purpose**: Search for tickets by ID number
- **Parameters**:
  - `cedula` (str): ID number to search
  - `show_all` (bool): Return all tickets or limit to 5
- **Returns**: `List[Dict[str, Any]]` - List of ticket dictionaries

**`search_incidents(cedula, show_all=False)`**
- **Purpose**: Search for incidents by ID number
- **Parameters**:
  - `cedula` (str): ID number to search
  - `show_all` (bool): Return all incidents or limit to 5
- **Returns**: `List[Dict[str, Any]]` - List of incident dictionaries

### ReportGenerator

Generates HTML reports from BMC Remedy data.

```python
from core.report_generator import ReportGenerator

# Initialize
generator = ReportGenerator(config)

# Generate HTML report
html_content = generator.generate_html_report(report_file_path)
```

#### Methods

**`generate_html_report(report_file)`**
- **Purpose**: Generate HTML report from JSON data
- **Parameters**: `report_file` (Path) - Path to JSON report file
- **Returns**: `str` - HTML content or None if generation fails

**`format_date(date_str)` (static)**
- **Purpose**: Format date from ISO to Colombian time (UTC-5)
- **Parameters**: `date_str` (str) - Date in ISO format
- **Returns**: `str` - Formatted date string

### EmailSender

Handles sending HTML reports via email.

```python
from core.email_sender import EmailSender

# Initialize
sender = EmailSender(config.email_config)

# Send report
success = sender.send_report(
    html_content="<html>...</html>",
    recipient="<EMAIL>",
    cedula="1152213619"
)
```

#### Methods

**`send_report(html_content, recipient=None, cedula="")`**
- **Purpose**: Send HTML report via email
- **Parameters**:
  - `html_content` (str): HTML content to send
  - `recipient` (str, optional): Email recipient
  - `cedula` (str): ID number for subject line
- **Returns**: `bool` - True if email sent successfully

**`test_connection()`**
- **Purpose**: Test email service connection
- **Returns**: `bool` - True if connection successful

## Configuration Classes

### Config

Centralized configuration management.

```python
from config.settings import Config, get_config

# Get global config instance
config = get_config()

# Access configuration
print(config.bmc_api_base_url)
print(config.email_default_recipient)
print(config.log_level)

# Get configuration dictionaries
bmc_config = config.bmc_config
email_config = config.email_config
logging_config = config.logging_config
```

#### Properties

**BMC Configuration**
- `bmc_api_base_url`: BMC Remedy API base URL
- `bmc_login_url`: BMC login endpoint
- `bmc_username`: BMC username
- `bmc_password`: BMC password
- `bmc_timeout`: Request timeout in seconds
- `bmc_verify_ssl`: SSL verification flag

**Email Configuration**
- `email_url`: Email service URL
- `email_default_recipient`: Default recipient email
- `email_timeout`: Email timeout in seconds
- `email_verify_ssl`: Email SSL verification flag

**Application Configuration**
- `app_name`: Application name
- `version`: Application version
- `debug`: Debug mode flag
- `silent_mode`: Silent mode flag
- `max_incidents_display`: Maximum incidents to display

**Logging Configuration**
- `log_level`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `log_output`: Output mode (console, file, both, silent)
- `log_dir`: Log directory path
- `enable_colors`: Color support flag

## Utility Functions

### Logging Utilities

```python
from utils.logging import setup_logging, get_logger

# Setup logging
setup_logging(config)

# Get logger
logger = get_logger(__name__)

# Log messages
logger.debug("Debug message")
logger.info("Info message")
logger.warning("Warning message")
logger.error("Error message")
logger.critical("Critical message")
```

### Formatting Utilities

```python
from utils.formatters import (
    format_date_colombia,
    create_table_from_data,
    truncate_text,
    format_status,
    format_priority
)

# Format date to Colombian time
formatted_date = format_date_colombia("2025-02-16T01:29:50.000+0000")
# Returns: "15/02/2025 20:29 (COL)"

# Create formatted table
table = create_table_from_data(
    data=[{"Name": "John", "Status": "Active"}],
    headers=["Name", "Status"],
    title="Users"
)

# Truncate long text
short_text = truncate_text("Very long text here...", max_length=20)

# Format status with emoji
status = format_status("Open")  # Returns: "🔴 Open"

# Format priority with emoji
priority = format_priority("High")  # Returns: "🔥 High"
```

## Data Structures

### User Information

```python
user_info = {
    'cedula': '1152213619',
    'name': 'John Doe',
    'Internet E-mail': '<EMAIL>',
    'Phone Number': '+57 ************',
    'Organization': 'IT Department'
}
```

### Ticket/Incident Structure

```python
ticket = {
    'Incident Number': 'INC000001234567',
    'Description': 'System access issue',
    'Status': 'Open',
    'Priority': 'Medium',
    'Submit Date': '2025-02-16T01:29:50.000+0000',
    'Last Modified Date': '2025-02-16T02:15:30.000+0000',
    'Assigned To': 'Support Team',
    'Category': 'Access Request'
}
```

### Report Data Structure

```python
report_data = {
    'user_info': user_info,
    'tickets': {
        'data': [ticket1, ticket2, ...],
        'count': 2
    },
    'incidents': {
        'data': [incident1, incident2, ...],
        'count': 3
    },
    'search_params': {
        'cedula': '1152213619',
        'show_all': False,
        'timestamp': '2025-02-16T07:30:00.000000'
    }
}
```

## Error Handling

### Exception Types

The application uses standard Python exceptions with detailed logging:

- `requests.exceptions.RequestException`: Network/API errors
- `json.JSONDecodeError`: JSON parsing errors
- `FileNotFoundError`: Missing configuration or data files
- `ValueError`: Invalid configuration values
- `Exception`: General application errors

### Error Response Format

```python
# API errors are logged with context
logger.error(f"BMC API error: {str(e)}")
logger.error(f"Request URL: {url}")
logger.error(f"Response status: {response.status_code}")
logger.error(f"Response body: {response.text}")
```

## Environment Variables

### Required Variables

```env
LOGIN_URL=https://bmc-server.com/api/jwt/login
API_BASE_URL=https://bmc-server.com/api
USERNAME_=service_account
PASSWORD=secure_password
EMAIL_URL=https://email-service.com/send
```

### Optional Variables

```env
EMAIL_TO=<EMAIL>
DEBUG=false
LOG_LEVEL=INFO
LOG_OUTPUT=both
SILENT_MODE=false
MAX_INCIDENTS_DISPLAY=5
BMC_TIMEOUT=30
BMC_VERIFY_SSL=false
EMAIL_TIMEOUT=10
EMAIL_VERIFY_SSL=false
ENABLE_COLORS=true
LOG_DIR=logs
```
