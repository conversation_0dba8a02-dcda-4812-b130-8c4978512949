#!/bin/bash

# Build script for BMC Remedy Optimized Container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="bmc-remedy-optimized"
VERSION=${VERSION:-"2.0.0"}
BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}BMC Remedy Optimized - Build Script${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}ERROR: Docker is not installed or not in PATH${NC}"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}ERROR: Docker Compose is not installed or not in PATH${NC}"
    exit 1
fi

# Navigate to project directory
cd "$(dirname "$0")/.."

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}WARNING: .env file not found${NC}"
    echo "Copying .env.example to .env..."
    cp .env.example .env
    echo ""
    echo -e "${YELLOW}IMPORTANT: Edit the .env file with your credentials before running${NC}"
    echo "Press Enter to continue or Ctrl+C to exit..."
    read
fi

echo -e "${GREEN}Building Docker image...${NC}"
echo "Version: $VERSION"
echo "Build Date: $BUILD_DATE"
echo ""

# Build the main image
docker-compose -f docker/docker-compose.yml build \
    --build-arg BUILD_DATE="$BUILD_DATE" \
    --build-arg VERSION="$VERSION" \
    bmc-remedy

if [ $? -ne 0 ]; then
    echo -e "${RED}ERROR: Failed to build Docker image${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo ""
echo -e "${BLUE}Usage examples:${NC}"
echo ""
echo "1. Basic search:"
echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619"
echo ""
echo "2. With specific recipient:"
echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619 --destinatario <EMAIL>"
echo ""
echo "3. Show all incidents:"
echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619 --todos"
echo ""
echo "4. Development mode:"
echo "   docker-compose -f docker/docker-compose.yml --profile dev up -d bmc-remedy-dev"
echo ""
echo "5. Run tests:"
echo "   docker-compose -f docker/docker-compose.yml --profile test run --rm bmc-remedy-test"
echo ""

# Ask if user wants to run a test
read -p "Would you like to run a test search? (y/n): " run_test
if [[ $run_test == "y" || $run_test == "Y" ]]; then
    read -p "Enter ID number: " cedula
    read -p "Enter recipient email (optional): " email
    
    if [ -z "$email" ]; then
        echo "Running: docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py $cedula"
        docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py $cedula
    else
        echo "Running: docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py $cedula --destinatario $email"
        docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py $cedula --destinatario $email
    fi
fi

echo ""
echo -e "${GREEN}Build script completed.${NC}"
