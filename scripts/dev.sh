#!/bin/bash

# Development script for BMC Remedy Optimized Container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}BMC Remedy Optimized - Development${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Navigate to project directory
cd "$(dirname "$0")/.."

# Function to show menu
show_menu() {
    echo -e "${BLUE}Development Options:${NC}"
    echo "1. Start development container"
    echo "2. Stop development container"
    echo "3. View logs"
    echo "4. Execute command in container"
    echo "5. Run tests"
    echo "6. Build and test"
    echo "7. Clean up containers"
    echo "8. Exit"
    echo ""
}

# Function to start dev container
start_dev() {
    echo -e "${GREEN}Starting development container...${NC}"
    docker-compose -f docker/docker-compose.yml --profile dev up -d bmc-remedy-dev
    echo -e "${GREEN}Development container started!${NC}"
    echo ""
    echo "To execute commands:"
    echo "docker-compose -f docker/docker-compose.yml exec bmc-remedy-dev bash"
}

# Function to stop dev container
stop_dev() {
    echo -e "${YELLOW}Stopping development container...${NC}"
    docker-compose -f docker/docker-compose.yml --profile dev down
    echo -e "${GREEN}Development container stopped!${NC}"
}

# Function to view logs
view_logs() {
    echo -e "${BLUE}Viewing logs...${NC}"
    docker-compose -f docker/docker-compose.yml --profile dev logs -f bmc-remedy-dev
}

# Function to execute command
exec_command() {
    read -p "Enter command to execute: " cmd
    echo -e "${BLUE}Executing: $cmd${NC}"
    docker-compose -f docker/docker-compose.yml exec bmc-remedy-dev $cmd
}

# Function to run tests
run_tests() {
    echo -e "${BLUE}Running tests...${NC}"
    docker-compose -f docker/docker-compose.yml --profile test run --rm bmc-remedy-test
}

# Function to build and test
build_and_test() {
    echo -e "${BLUE}Building and testing...${NC}"
    ./scripts/build.sh
    run_tests
}

# Function to clean up
cleanup() {
    echo -e "${YELLOW}Cleaning up containers...${NC}"
    docker-compose -f docker/docker-compose.yml --profile dev --profile test down
    docker system prune -f
    echo -e "${GREEN}Cleanup completed!${NC}"
}

# Main menu loop
while true; do
    show_menu
    read -p "Select option (1-8): " choice
    
    case $choice in
        1)
            start_dev
            ;;
        2)
            stop_dev
            ;;
        3)
            view_logs
            ;;
        4)
            exec_command
            ;;
        5)
            run_tests
            ;;
        6)
            build_and_test
            ;;
        7)
            cleanup
            ;;
        8)
            echo -e "${GREEN}Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid option. Please select 1-8.${NC}"
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
    echo ""
done
