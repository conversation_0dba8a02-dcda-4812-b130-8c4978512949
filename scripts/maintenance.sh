#!/bin/bash

# Maintenance script for BMC Remedy Optimized Container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}BMC Remedy Optimized - Maintenance${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Navigate to project directory
cd "$(dirname "$0")/.."

# Function to show maintenance menu
show_maintenance_menu() {
    echo -e "${BLUE}Maintenance Options:${NC}"
    echo "1. Clean logs"
    echo "2. Clean Docker resources"
    echo "3. Update dependencies"
    echo "4. Backup configuration"
    echo "5. Restore configuration"
    echo "6. Health check"
    echo "7. Performance check"
    echo "8. Security audit"
    echo "9. Full maintenance"
    echo "10. Exit"
    echo ""
}

# Function to clean logs
clean_logs() {
    echo -e "${GREEN}Cleaning logs...${NC}"
    
    if [ -d "logs" ]; then
        # Archive logs older than 30 days
        find logs/ -name "*.log.*" -mtime +30 -exec rm {} \;
        
        # Compress current log if it's large (>10MB)
        if [ -f "logs/bmc-remedy.log" ]; then
            size=$(stat -f%z "logs/bmc-remedy.log" 2>/dev/null || stat -c%s "logs/bmc-remedy.log" 2>/dev/null || echo 0)
            if [ "$size" -gt 10485760 ]; then
                gzip "logs/bmc-remedy.log"
                touch "logs/bmc-remedy.log"
            fi
        fi
        
        echo -e "${GREEN}Logs cleaned successfully${NC}"
    else
        echo -e "${YELLOW}No logs directory found${NC}"
    fi
}

# Function to clean Docker resources
clean_docker() {
    echo -e "${GREEN}Cleaning Docker resources...${NC}"
    
    # Stop and remove containers
    docker-compose -f docker/docker-compose.yml down --remove-orphans
    
    # Remove unused images
    docker image prune -f
    
    # Remove unused volumes
    docker volume prune -f
    
    # Remove unused networks
    docker network prune -f
    
    echo -e "${GREEN}Docker resources cleaned${NC}"
}

# Function to update dependencies
update_dependencies() {
    echo -e "${GREEN}Updating dependencies...${NC}"
    
    # Backup current requirements
    cp requirements.txt requirements.txt.backup
    
    # Update pip
    pip install --upgrade pip
    
    # Install/update requirements
    pip install -r requirements.txt --upgrade
    
    # Generate new requirements with current versions
    pip freeze > requirements.new.txt
    
    echo -e "${GREEN}Dependencies updated${NC}"
    echo -e "${YELLOW}New requirements saved to requirements.new.txt${NC}"
    echo -e "${YELLOW}Review and replace requirements.txt if needed${NC}"
}

# Function to backup configuration
backup_config() {
    echo -e "${GREEN}Backing up configuration...${NC}"
    
    BACKUP_DIR="backup/$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup configuration files
    [ -f ".env" ] && cp .env "$BACKUP_DIR/"
    [ -f "requirements.txt" ] && cp requirements.txt "$BACKUP_DIR/"
    [ -d "logs" ] && cp -r logs "$BACKUP_DIR/"
    
    # Create backup archive
    tar -czf "$BACKUP_DIR.tar.gz" "$BACKUP_DIR"
    rm -rf "$BACKUP_DIR"
    
    echo -e "${GREEN}Configuration backed up to $BACKUP_DIR.tar.gz${NC}"
}

# Function to restore configuration
restore_config() {
    echo "Available backups:"
    ls -la backup/*.tar.gz 2>/dev/null || echo "No backups found"
    echo ""
    
    read -p "Enter backup file name (e.g., backup/20250216-143000.tar.gz): " backup_file
    
    if [ -f "$backup_file" ]; then
        echo -e "${GREEN}Restoring from $backup_file...${NC}"
        
        # Extract backup
        tar -xzf "$backup_file"
        backup_dir=$(basename "$backup_file" .tar.gz)
        
        # Restore files
        [ -f "$backup_dir/.env" ] && cp "$backup_dir/.env" .
        [ -f "$backup_dir/requirements.txt" ] && cp "$backup_dir/requirements.txt" .
        [ -d "$backup_dir/logs" ] && cp -r "$backup_dir/logs" .
        
        # Cleanup
        rm -rf "$backup_dir"
        
        echo -e "${GREEN}Configuration restored successfully${NC}"
    else
        echo -e "${RED}Backup file not found: $backup_file${NC}"
    fi
}

# Function to perform health check
health_check() {
    echo -e "${GREEN}Performing health check...${NC}"
    
    # Check configuration file
    if [ -f ".env" ]; then
        echo "✅ Configuration file exists"
    else
        echo "❌ Configuration file missing"
    fi
    
    # Check Docker
    if command -v docker &> /dev/null; then
        echo "✅ Docker is available"
        if docker info &> /dev/null; then
            echo "✅ Docker daemon is running"
        else
            echo "❌ Docker daemon is not running"
        fi
    else
        echo "❌ Docker is not installed"
    fi
    
    # Check Python dependencies
    if python -c "import requests, dotenv, prettytable, colorama" 2>/dev/null; then
        echo "✅ Core dependencies are available"
    else
        echo "❌ Some core dependencies are missing"
    fi
    
    # Check logs directory
    if [ -d "logs" ]; then
        echo "✅ Logs directory exists"
        log_count=$(find logs/ -name "*.log" | wc -l)
        echo "📊 Found $log_count log files"
    else
        echo "⚠️  Logs directory not found"
    fi
    
    # Check disk space
    disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        echo "✅ Disk space OK ($disk_usage% used)"
    else
        echo "⚠️  Disk space high ($disk_usage% used)"
    fi
}

# Function to perform performance check
performance_check() {
    echo -e "${GREEN}Performing performance check...${NC}"
    
    # Check system resources
    echo "System Information:"
    echo "CPU cores: $(nproc)"
    echo "Memory: $(free -h | grep '^Mem:' | awk '{print $2}' || echo 'N/A')"
    echo "Disk space: $(df -h . | tail -1 | awk '{print $4}' || echo 'N/A')"
    
    # Check Docker image size
    if docker images bmc-remedy-optimized &> /dev/null; then
        image_size=$(docker images bmc-remedy-optimized --format "table {{.Size}}" | tail -1)
        echo "Docker image size: $image_size"
    fi
    
    # Check log file sizes
    if [ -d "logs" ]; then
        echo "Log file sizes:"
        du -h logs/* 2>/dev/null || echo "No log files found"
    fi
}

# Function to perform security audit
security_audit() {
    echo -e "${GREEN}Performing security audit...${NC}"
    
    # Check file permissions
    echo "Checking file permissions..."
    
    if [ -f ".env" ]; then
        env_perms=$(stat -c "%a" .env 2>/dev/null || stat -f "%A" .env 2>/dev/null)
        if [ "$env_perms" = "600" ] || [ "$env_perms" = "-rw-------" ]; then
            echo "✅ .env file permissions are secure"
        else
            echo "⚠️  .env file permissions should be 600"
        fi
    fi
    
    # Check for sensitive data in logs
    if [ -d "logs" ]; then
        echo "Checking for sensitive data in logs..."
        if grep -r -i "password\|secret\|key\|token" logs/ &> /dev/null; then
            echo "⚠️  Potential sensitive data found in logs"
        else
            echo "✅ No obvious sensitive data in logs"
        fi
    fi
    
    # Check Docker security
    echo "Docker security recommendations:"
    echo "- Run containers as non-root user ✅"
    echo "- Use specific image tags ✅"
    echo "- Limit container resources ✅"
    echo "- Use secrets for sensitive data ⚠️  (recommended)"
}

# Function to perform full maintenance
full_maintenance() {
    echo -e "${GREEN}Performing full maintenance...${NC}"
    
    backup_config
    clean_logs
    clean_docker
    health_check
    performance_check
    security_audit
    
    echo -e "${GREEN}Full maintenance completed!${NC}"
}

# Main menu loop
while true; do
    show_maintenance_menu
    read -p "Select option (1-10): " choice
    
    case $choice in
        1)
            clean_logs
            ;;
        2)
            clean_docker
            ;;
        3)
            update_dependencies
            ;;
        4)
            backup_config
            ;;
        5)
            restore_config
            ;;
        6)
            health_check
            ;;
        7)
            performance_check
            ;;
        8)
            security_audit
            ;;
        9)
            full_maintenance
            ;;
        10)
            echo -e "${GREEN}Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid option. Please select 1-10.${NC}"
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
    echo ""
done
