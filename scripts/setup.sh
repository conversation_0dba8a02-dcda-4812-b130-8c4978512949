#!/bin/bash

# Setup script for BMC Remedy Optimized Container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}BMC Remedy Optimized - Setup${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Navigate to project directory
cd "$(dirname "$0")/.."

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}Checking prerequisites...${NC}"
    
    # Check Docker
    if command -v docker &> /dev/null; then
        echo "✅ Docker is installed"
        docker_version=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        echo "   Version: $docker_version"
    else
        echo -e "${RED}❌ Docker is not installed${NC}"
        echo "Please install Docker from https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        echo "✅ Docker Compose is installed"
        compose_version=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        echo "   Version: $compose_version"
    else
        echo -e "${RED}❌ Docker Compose is not installed${NC}"
        echo "Please install Docker Compose from https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    # Check Python (for local development)
    if command -v python3 &> /dev/null; then
        echo "✅ Python 3 is available"
        python_version=$(python3 --version | cut -d' ' -f2)
        echo "   Version: $python_version"
    else
        echo -e "${YELLOW}⚠️  Python 3 not found (optional for containerized usage)${NC}"
    fi
    
    echo ""
}

# Function to setup environment
setup_environment() {
    echo -e "${BLUE}Setting up environment...${NC}"
    
    # Create .env file if it doesn't exist
    if [ ! -f ".env" ]; then
        echo "Creating .env file from template..."
        cp .env.example .env
        echo -e "${YELLOW}⚠️  Please edit .env file with your credentials${NC}"
    else
        echo "✅ .env file already exists"
    fi
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p backup
    mkdir -p tests/fixtures
    
    echo "✅ Directories created"
    echo ""
}

# Function to build Docker image
build_image() {
    echo -e "${BLUE}Building Docker image...${NC}"
    
    # Build the image
    docker-compose -f docker/docker-compose.yml build
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Docker image built successfully${NC}"
    else
        echo -e "${RED}❌ Failed to build Docker image${NC}"
        exit 1
    fi
    
    echo ""
}

# Function to run tests
run_initial_tests() {
    echo -e "${BLUE}Running initial tests...${NC}"
    
    # Run basic tests
    if docker-compose -f docker/docker-compose.yml --profile test run --rm bmc-remedy-test; then
        echo -e "${GREEN}✅ Tests passed${NC}"
    else
        echo -e "${YELLOW}⚠️  Some tests failed (this might be expected without proper configuration)${NC}"
    fi
    
    echo ""
}

# Function to show usage examples
show_usage() {
    echo -e "${BLUE}Usage Examples:${NC}"
    echo ""
    echo "1. Basic search:"
    echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619"
    echo ""
    echo "2. With specific recipient:"
    echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619 --destinatario <EMAIL>"
    echo ""
    echo "3. Show all incidents:"
    echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619 --todos"
    echo ""
    echo "4. Development mode:"
    echo "   ./scripts/dev.sh"
    echo ""
    echo "5. Run tests:"
    echo "   ./scripts/test.sh"
    echo ""
    echo "6. Maintenance:"
    echo "   ./scripts/maintenance.sh"
    echo ""
}

# Function to show next steps
show_next_steps() {
    echo -e "${BLUE}Next Steps:${NC}"
    echo ""
    echo "1. Edit the .env file with your BMC Remedy credentials:"
    echo "   nano .env"
    echo ""
    echo "2. Test the configuration:"
    echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py --help"
    echo ""
    echo "3. Run a test search (replace with actual ID):"
    echo "   docker-compose -f docker/docker-compose.yml run --rm bmc-remedy python main.py 1152213619"
    echo ""
    echo "4. For development:"
    echo "   ./scripts/dev.sh"
    echo ""
    echo "5. For maintenance:"
    echo "   ./scripts/maintenance.sh"
    echo ""
}

# Main setup process
main() {
    echo -e "${GREEN}Starting BMC Remedy Optimized setup...${NC}"
    echo ""
    
    check_prerequisites
    setup_environment
    
    # Ask if user wants to build image
    read -p "Build Docker image now? (y/n): " build_now
    if [[ $build_now == "y" || $build_now == "Y" ]]; then
        build_image
        
        # Ask if user wants to run tests
        read -p "Run initial tests? (y/n): " test_now
        if [[ $test_now == "y" || $test_now == "Y" ]]; then
            run_initial_tests
        fi
    fi
    
    show_usage
    show_next_steps
    
    echo -e "${GREEN}Setup completed successfully!${NC}"
    echo ""
    echo -e "${YELLOW}Don't forget to edit the .env file with your credentials before running the application.${NC}"
}

# Run main function
main
