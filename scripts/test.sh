#!/bin/bash

# Test script for BMC Remedy Optimized Container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}BMC Remedy Optimized - Test Suite${NC}"
echo -e "${BLUE}======================================${NC}"
echo ""

# Navigate to project directory
cd "$(dirname "$0")/.."

# Function to show test menu
show_test_menu() {
    echo -e "${BLUE}Test Options:${NC}"
    echo "1. Run all tests"
    echo "2. Run unit tests only"
    echo "3. Run integration tests only"
    echo "4. Run tests with coverage"
    echo "5. Run specific test file"
    echo "6. Run tests in Docker"
    echo "7. Lint code"
    echo "8. Type check"
    echo "9. Exit"
    echo ""
}

# Function to run all tests
run_all_tests() {
    echo -e "${GREEN}Running all tests...${NC}"
    python -m pytest tests/ -v
}

# Function to run unit tests
run_unit_tests() {
    echo -e "${GREEN}Running unit tests...${NC}"
    python -m pytest tests/ -v -m "unit or not integration"
}

# Function to run integration tests
run_integration_tests() {
    echo -e "${GREEN}Running integration tests...${NC}"
    python -m pytest tests/ -v -m "integration"
}

# Function to run tests with coverage
run_tests_with_coverage() {
    echo -e "${GREEN}Running tests with coverage...${NC}"
    
    # Check if coverage is installed
    if ! python -c "import coverage" 2>/dev/null; then
        echo -e "${YELLOW}Installing coverage...${NC}"
        pip install coverage
    fi
    
    coverage run -m pytest tests/
    coverage report -m
    coverage html
    
    echo -e "${GREEN}Coverage report generated in htmlcov/index.html${NC}"
}

# Function to run specific test file
run_specific_test() {
    echo "Available test files:"
    find tests/ -name "test_*.py" -exec basename {} \;
    echo ""
    read -p "Enter test file name (e.g., test_config.py): " test_file
    
    if [ -f "tests/$test_file" ]; then
        echo -e "${GREEN}Running $test_file...${NC}"
        python -m pytest "tests/$test_file" -v
    else
        echo -e "${RED}Test file not found: tests/$test_file${NC}"
    fi
}

# Function to run tests in Docker
run_tests_docker() {
    echo -e "${GREEN}Running tests in Docker...${NC}"
    
    # Check if Docker is available
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}Docker Compose not found${NC}"
        return 1
    fi
    
    docker-compose -f docker/docker-compose.yml --profile test run --rm bmc-remedy-test
}

# Function to lint code
lint_code() {
    echo -e "${GREEN}Linting code...${NC}"
    
    # Check if flake8 is installed
    if ! python -c "import flake8" 2>/dev/null; then
        echo -e "${YELLOW}Installing flake8...${NC}"
        pip install flake8
    fi
    
    echo "Running flake8..."
    flake8 src/ tests/ main.py --max-line-length=100 --ignore=E203,W503
    
    # Check if black is installed
    if ! python -c "import black" 2>/dev/null; then
        echo -e "${YELLOW}Installing black...${NC}"
        pip install black
    fi
    
    echo "Running black (check only)..."
    black --check --diff src/ tests/ main.py
    
    echo -e "${GREEN}Linting completed${NC}"
}

# Function to type check
type_check() {
    echo -e "${GREEN}Running type checks...${NC}"
    
    # Check if mypy is installed
    if ! python -c "import mypy" 2>/dev/null; then
        echo -e "${YELLOW}Installing mypy...${NC}"
        pip install mypy
    fi
    
    mypy src/ --ignore-missing-imports --no-strict-optional
    
    echo -e "${GREEN}Type checking completed${NC}"
}

# Function to setup test environment
setup_test_env() {
    echo -e "${BLUE}Setting up test environment...${NC}"
    
    # Install test dependencies
    pip install pytest pytest-cov coverage flake8 black mypy
    
    # Create test directories if they don't exist
    mkdir -p tests/fixtures
    mkdir -p tests/integration
    mkdir -p htmlcov
    
    echo -e "${GREEN}Test environment setup completed${NC}"
}

# Check if pytest is available
if ! python -c "import pytest" 2>/dev/null; then
    echo -e "${YELLOW}pytest not found. Setting up test environment...${NC}"
    setup_test_env
fi

# Main menu loop
while true; do
    show_test_menu
    read -p "Select option (1-9): " choice
    
    case $choice in
        1)
            run_all_tests
            ;;
        2)
            run_unit_tests
            ;;
        3)
            run_integration_tests
            ;;
        4)
            run_tests_with_coverage
            ;;
        5)
            run_specific_test
            ;;
        6)
            run_tests_docker
            ;;
        7)
            lint_code
            ;;
        8)
            type_check
            ;;
        9)
            echo -e "${GREEN}Goodbye!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Invalid option. Please select 1-9.${NC}"
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
    echo ""
done
