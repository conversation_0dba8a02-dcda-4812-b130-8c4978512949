#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Configuration Setup Script for BMC Remedy Automation Tool

This script helps you set up the .env configuration file with proper values.
Run this script to interactively configure your BMC Remedy automation tool.
"""

import os
import sys
from pathlib import Path
import getpass


def create_env_file():
    """Create .env file with user input."""
    print("🔧 BMC Remedy Configuration Setup")
    print("=" * 50)
    print("This script will help you create a .env configuration file.")
    print("Please provide the following information:\n")
    
    # BMC Remedy Configuration
    print("📡 BMC Remedy Server Configuration:")
    login_url = input("Enter BMC Login URL (e.g., https://your-server.com/api/jwt/login): ").strip()
    api_base_url = input("Enter BMC API Base URL (e.g., https://your-server.com/api): ").strip()
    username = input("Enter BMC Username: ").strip()
    password = getpass.getpass("Enter BMC Password: ").strip()
    
    print("\n📧 Email Configuration:")
    email_url = input("Enter Email Service URL (e.g., https://your-email-service.com/send): ").strip()
    email_to = input("Enter Default Email Recipient: ").strip()
    
    print("\n⚙️ Application Settings:")
    debug = input("Enable Debug Mode? (y/N): ").strip().lower() in ['y', 'yes']
    max_incidents = input("Max Incidents to Display (default: 5): ").strip() or "5"
    
    # Validate required fields
    required_fields = {
        "LOGIN_URL": login_url,
        "API_BASE_URL": api_base_url,
        "USERNAME_": username,
        "PASSWORD": password,
        "EMAIL_URL": email_url,
        "EMAIL_TO": email_to
    }
    
    missing_fields = [field for field, value in required_fields.items() if not value]
    if missing_fields:
        print(f"\n❌ Error: Missing required fields: {', '.join(missing_fields)}")
        return False
    
    # Create .env content
    env_content = f"""# BMC Remedy Configuration
LOGIN_URL={login_url}
API_BASE_URL={api_base_url}
USERNAME_={username}
PASSWORD={password}

# Email Configuration
EMAIL_URL={email_url}
EMAIL_TO={email_to}

# Application Settings
APP_NAME=bmc-remedy
APP_VERSION=2.0.0
DEBUG={'true' if debug else 'false'}
SILENT_MODE=false
MAX_INCIDENTS_DISPLAY={max_incidents}

# Logging Configuration
LOG_LEVEL={'DEBUG' if debug else 'INFO'}
LOG_OUTPUT=both
LOG_DIR=logs
ENABLE_COLORS=true

# BMC Settings
BMC_TIMEOUT=30
BMC_VERIFY_SSL=false

# Email Settings
EMAIL_TIMEOUT=10
EMAIL_VERIFY_SSL=false
"""
    
    # Write .env file
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("\n✅ Configuration file '.env' created successfully!")
        print("\n📋 Next steps:")
        print("1. Test the configuration: python main.py <cedula>")
        print("2. Check logs in 'logs/' directory if there are issues")
        print("3. Use --debug flag for detailed troubleshooting")
        return True
    except Exception as e:
        print(f"\n❌ Error creating .env file: {e}")
        return False


def check_existing_config():
    """Check if .env file already exists."""
    if Path('.env').exists():
        print("⚠️  A .env file already exists.")
        overwrite = input("Do you want to overwrite it? (y/N): ").strip().lower()
        return overwrite in ['y', 'yes']
    return True


def validate_config():
    """Validate the current configuration."""
    if not Path('.env').exists():
        print("❌ No .env file found. Please run the setup first.")
        return False
    
    print("🔍 Validating configuration...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv('.env')
    
    required_vars = [
        'LOGIN_URL', 'API_BASE_URL', 'USERNAME_', 'PASSWORD', 
        'EMAIL_URL', 'EMAIL_TO'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var, '').strip()
        if not value:
            missing_vars.append(var)
        elif var in ['LOGIN_URL', 'API_BASE_URL', 'EMAIL_URL']:
            if not value.startswith(('http://', 'https://')):
                print(f"⚠️  Warning: {var} should start with http:// or https://")
    
    if missing_vars:
        print(f"❌ Missing required variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Configuration validation passed!")
    return True


def main():
    """Main function."""
    if len(sys.argv) > 1 and sys.argv[1] == '--validate':
        validate_config()
        return
    
    print("🚀 BMC Remedy Automation Tool - Configuration Setup")
    print("=" * 60)
    
    if not check_existing_config():
        print("Setup cancelled.")
        return
    
    if create_env_file():
        print("\n🎉 Setup completed successfully!")
        print("\nYou can now run the BMC Remedy automation tool:")
        print("  python main.py <cedula>")
        print("  python main.py <cedula> --destinatario <EMAIL>")
        print("  python main.py <cedula> --todos --debug")
    else:
        print("\n❌ Setup failed. Please try again.")


if __name__ == "__main__":
    main()
