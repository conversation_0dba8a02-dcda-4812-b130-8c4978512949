#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Optimized BMC Remedy client for API interactions.
Handles authentication, user search, and ticket/incident retrieval.
"""

import json
import requests
from datetime import datetime
from typing import Optional, Dict, List, Any
from urllib3.exceptions import InsecureRequestWarning

from utils.logging import get_logger, log_function_call

# Disable SSL warnings if SSL verification is disabled
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class BMCAuthenticator:
    """Handles JWT authentication for BMC Remedy."""
    
    def __init__(self, login_url: str, username: str, password: str, verify_ssl: bool = False):
        """Initialize authenticator."""
        self.login_url = login_url
        self.username = username
        self.password = password
        self.verify_ssl = verify_ssl
        self.logger = get_logger(__name__)
    
    @log_function_call
    def get_token(self) -> Optional[str]:
        """
        Get JW<PERSON> token from BMC Remedy.
        
        Returns:
            JWT token or None if authentication fails
        """
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        payload = {
            'username': self.username,
            'password': self.password
        }
        
        try:
            response = requests.post(
                self.login_url,
                headers=headers,
                data=payload,
                verify=self.verify_ssl,
                timeout=30
            )
            response.raise_for_status()
            
            self.logger.info("Authentication successful")
            return response.text
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Authentication failed: {str(e)}")
            return None


class BMCClient:
    """Main client for BMC Remedy API interactions."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize BMC client.
        
        Args:
            config: BMC configuration dictionary
        """
        self.api_base_url = config['api_base_url']
        self.timeout = config['timeout']
        self.verify_ssl = config['verify_ssl']
        self.logger = get_logger(__name__)
        
        # Initialize authenticator
        self.authenticator = BMCAuthenticator(
            login_url=config['login_url'],
            username=config['username'],
            password=config['password'],
            verify_ssl=self.verify_ssl
        )
        
        # Get authentication token
        self.token = self.authenticator.get_token()
        if not self.token:
            raise Exception("Failed to authenticate with BMC Remedy")
        
        # Setup headers
        self.headers = {
            'Authorization': f'AR-JWT {self.token}',
            'Content-Type': 'application/json'
        }
    
    @log_function_call
    def search_user_by_cedula(self, cedula: str) -> Optional[Dict[str, Any]]:
        """
        Search for user by cedula in CTM:People.
        
        Args:
            cedula: ID number to search
            
        Returns:
            User information dictionary or None if not found
        """
        url = f"{self.api_base_url}/arsys/v1/entry/CTM:People"
        params = {
            'q': f'\'Corporate ID\'="{cedula}"',
            'limit': '1'
        }
        
        try:
            response = requests.get(
                url,
                headers=self.headers,
                params=params,
                verify=self.verify_ssl,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            entries = data.get('entries', [])
            
            if entries:
                entry = entries[0]['values']
                user_info = {
                    'cedula': cedula,
                    'name': f"{entry.get('First Name', '')} {entry.get('Last Name', '')}".strip(),
                    'Internet E-mail': entry.get('Internet E-mail', 'N/A'),
                    'Phone Number': entry.get('Phone Number', 'N/A'),
                    'Organization': entry.get('Organization', 'N/A')
                }
                self.logger.info(f"User found: {user_info['name']}")
                return user_info
            else:
                self.logger.warning(f"No user found for cedula: {cedula}")
                return None
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error searching user: {str(e)}")
            return None
    
    @log_function_call
    def search_tickets(self, cedula: str, show_all: bool = False) -> List[Dict[str, Any]]:
        """
        Search for tickets by cedula.
        
        Args:
            cedula: ID number to search
            show_all: Whether to return all tickets or limit to 5
            
        Returns:
            List of ticket dictionaries
        """
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:Help Desk"
        limit = '0' if show_all else '5'
        
        params = {
            'q': f'\'Customer Login ID\'="{cedula}"',
            'limit': limit,
            'sort': 'Submit Date.desc'
        }
        
        return self._search_entries(url, params, "tickets")
    
    @log_function_call
    def search_incidents(self, cedula: str, show_all: bool = False) -> List[Dict[str, Any]]:
        """
        Search for incidents by cedula.
        
        Args:
            cedula: ID number to search
            show_all: Whether to return all incidents or limit to 5
            
        Returns:
            List of incident dictionaries
        """
        url = f"{self.api_base_url}/arsys/v1/entry/HPD:IncidentInterface"
        limit = '0' if show_all else '5'
        
        params = {
            'q': f'\'Login ID\'="{cedula}"',
            'limit': limit,
            'sort': 'Submit Date.desc'
        }
        
        return self._search_entries(url, params, "incidents")
    
    def _search_entries(self, url: str, params: Dict[str, str], entry_type: str) -> List[Dict[str, Any]]:
        """
        Generic method to search entries.
        
        Args:
            url: API endpoint URL
            params: Query parameters
            entry_type: Type of entries (for logging)
            
        Returns:
            List of entry dictionaries
        """
        try:
            response = requests.get(
                url,
                headers=self.headers,
                params=params,
                verify=self.verify_ssl,
                timeout=self.timeout
            )
            response.raise_for_status()
            
            data = response.json()
            entries = data.get('entries', [])
            
            # Extract values from entries
            results = []
            for entry in entries:
                results.append(entry.get('values', {}))
            
            self.logger.info(f"Found {len(results)} {entry_type}")
            return results
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error searching {entry_type}: {str(e)}")
            return []
    
    @staticmethod
    def get_current_timestamp() -> str:
        """Get current timestamp in ISO format."""
        return datetime.now().isoformat()
