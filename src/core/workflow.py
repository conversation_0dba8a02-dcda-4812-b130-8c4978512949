#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Main workflow manager for BMC Remedy automation.
Orchestrates the complete process of searching cases and sending reports.
"""

import json
from pathlib import Path
from typing import Optional, Tuple

from .bmc_client import BMCClient
from .report_generator import ReportGenerator
from .email_sender import <PERSON>ail<PERSON>ender
from utils.logging import get_logger, log_function_call


class WorkflowManager:
    """Manages the complete BMC Remedy automation workflow."""
    
    def __init__(self, config):
        """
        Initialize workflow manager.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # Initialize components
        self.bmc_client = BMCClient(config.bmc_config)
        self.report_generator = ReportGenerator(config)
        self.email_sender = EmailSender(config.email_config)
        
        # Output file for unified report
        self.report_file = Path("reporte_unificado.json")
    
    @log_function_call
    def run_complete_workflow(self, cedula: str, destinatario: Optional[str] = None, 
                            show_all: bool = False) -> bool:
        """
        Execute the complete workflow.
        
        Args:
            cedula: ID number to search
            destinatario: Email recipient (optional)
            show_all: Whether to show all incidents or limit to first 5
            
        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Starting complete workflow for cedula: {cedula}")
        
        try:
            # Step 1: Search for cases in BMC Remedy
            success, tickets_count, incidents_count = self._search_cases(cedula, show_all)
            if not success:
                return False
            
            # Step 2: Get recipient email if not provided
            if not destinatario:
                destinatario = self._get_user_email(cedula)
                if destinatario:
                    self.logger.info(f"Using user email: {destinatario}")
            
            # Step 3: Generate and send report
            success = self._generate_and_send_report(cedula, destinatario)
            if not success:
                return False
            
            self.logger.info(f"Workflow completed successfully - tickets: {tickets_count}, incidents: {incidents_count}")
            return True
            
        except Exception as e:
            self.logger.error(f"Workflow failed: {str(e)}")
            return False
    
    def _search_cases(self, cedula: str, show_all: bool) -> Tuple[bool, int, int]:
        """
        Search for cases in BMC Remedy.
        
        Args:
            cedula: ID number to search
            show_all: Whether to show all incidents
            
        Returns:
            Tuple of (success, tickets_count, incidents_count)
        """
        self.logger.info(f"Searching cases for cedula: {cedula}")
        
        try:
            # Search for user information
            user_info = self.bmc_client.search_user_by_cedula(cedula)
            if not user_info:
                self.logger.warning(f"No user found for cedula: {cedula}")
                user_info = {"cedula": cedula, "name": "N/A", "email": "N/A"}
            
            # Search for tickets and incidents
            tickets = self.bmc_client.search_tickets(cedula, show_all)
            incidents = self.bmc_client.search_incidents(cedula, show_all)
            
            # Create unified report data
            report_data = {
                "user_info": user_info,
                "tickets": {"data": tickets, "count": len(tickets)},
                "incidents": {"data": incidents, "count": len(incidents)},
                "search_params": {
                    "cedula": cedula,
                    "show_all": show_all,
                    "timestamp": self.bmc_client.get_current_timestamp()
                }
            }
            
            # Save unified report
            with open(self.report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Cases found - tickets: {len(tickets)}, incidents: {len(incidents)}")
            return True, len(tickets), len(incidents)
            
        except Exception as e:
            self.logger.error(f"Error searching cases: {str(e)}")
            return False, 0, 0
    
    def _get_user_email(self, cedula: str) -> Optional[str]:
        """
        Get user email from the report data.
        
        Args:
            cedula: ID number
            
        Returns:
            User email or None if not found
        """
        if not self.report_file.exists():
            return None
        
        try:
            with open(self.report_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            user_info = data.get('user_info', {})
            email = user_info.get('Internet E-mail')
            
            if email and email != 'N/A':
                return email
                
        except Exception as e:
            self.logger.warning(f"Error reading user email: {str(e)}")
        
        return None
    
    def _generate_and_send_report(self, cedula: str, destinatario: Optional[str]) -> bool:
        """
        Generate HTML report and send via email.
        
        Args:
            cedula: ID number
            destinatario: Email recipient
            
        Returns:
            True if successful, False otherwise
        """
        self.logger.info(f"Generating and sending report for cedula: {cedula}")
        
        try:
            # Generate HTML report
            html_content = self.report_generator.generate_html_report(self.report_file)
            if not html_content:
                self.logger.error("Failed to generate HTML report")
                return False
            
            # Send email
            success = self.email_sender.send_report(
                html_content=html_content,
                recipient=destinatario,
                cedula=cedula
            )
            
            if success:
                self.logger.info("Report sent successfully")
                return True
            else:
                self.logger.error("Failed to send report")
                return False
                
        except Exception as e:
            self.logger.error(f"Error generating/sending report: {str(e)}")
            return False
