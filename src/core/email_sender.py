#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Optimized email sender for BMC Remedy reports.
Handles HTML email sending with proper error handling.
"""

import requests
from typing import Optional, Dict, Any
from urllib3.exceptions import InsecureRequestWarning

from utils.logging import get_logger, log_function_call

# Disable SSL warnings if SSL verification is disabled
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)


class EmailSender:
    """Handles sending HTML reports via email."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize email sender.
        
        Args:
            config: Email configuration dictionary
        """
        self.mail_url = config['mail_url']
        self.default_recipient = config['default_recipient']
        self.timeout = config['timeout']
        self.verify_ssl = config['verify_ssl']
        self.logger = get_logger(__name__)
    
    @log_function_call
    def send_report(self, html_content: str, recipient: Optional[str] = None, 
                   cedula: str = "") -> bool:
        """
        Send HTML report via email.
        
        Args:
            html_content: HTML content to send
            recipient: Email recipient (optional, uses default if not provided)
            cedula: ID number for subject line
            
        Returns:
            True if email sent successfully, False otherwise
        """
        # Determine recipient
        to_email = recipient or self.default_recipient
        if not to_email:
            self.logger.error("No recipient email specified")
            return False
        
        # Prepare email data
        subject = f"Reporte BMC Remedy - Cédula {cedula}" if cedula else "Reporte BMC Remedy"
        
        email_data = {
            'to': to_email,
            'subject': subject,
            'html': html_content,
            'from': 'BMC Remedy System <<EMAIL>>'
        }
        
        try:
            self.logger.info(f"Sending email to: {to_email}")
            
            response = requests.post(
                self.mail_url,
                json=email_data,
                verify=self.verify_ssl,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                self.logger.info("Email sent successfully")
                return True
            else:
                self.logger.error(f"Email sending failed with status code: {response.status_code}")
                if response.text:
                    self.logger.error(f"Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error sending email: {str(e)}")
            return False
    
    def test_connection(self) -> bool:
        """
        Test email service connection.
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Simple GET request to test connectivity
            response = requests.get(
                self.mail_url.replace('/send', '/health'),
                verify=self.verify_ssl,
                timeout=self.timeout
            )
            return response.status_code < 400
        except requests.exceptions.RequestException:
            return False
