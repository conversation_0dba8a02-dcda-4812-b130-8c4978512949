#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Optimized HTML report generator for BMC Remedy data.
Generates formatted HTML reports with Colombian timezone support.
"""

import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional

from utils.logging import get_logger, log_function_call


class ReportGenerator:
    """Generates HTML reports from BMC Remedy data."""
    
    def __init__(self, config):
        """Initialize report generator."""
        self.config = config
        self.logger = get_logger(__name__)
    
    @log_function_call
    def generate_html_report(self, report_file: Path) -> Optional[str]:
        """
        Generate HTML report from JSON data.
        
        Args:
            report_file: Path to JSON report file
            
        Returns:
            HTML content string or None if generation fails
        """
        if not report_file.exists():
            self.logger.error(f"Report file not found: {report_file}")
            return None
        
        try:
            with open(report_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            html_content = self._build_html_report(data)
            self.logger.info("HTML report generated successfully")
            return html_content
            
        except Exception as e:
            self.logger.error(f"Error generating HTML report: {str(e)}")
            return None
    
    def _build_html_report(self, data: Dict[str, Any]) -> str:
        """Build complete HTML report."""
        user_info = data.get('user_info', {})
        tickets = data.get('tickets', {}).get('data', [])
        incidents = data.get('incidents', {}).get('data', [])
        search_params = data.get('search_params', {})
        
        html_parts = [
            self._get_html_header(),
            self._build_user_section(user_info, search_params),
            self._build_summary_section(len(tickets), len(incidents)),
            self._build_tickets_section(tickets),
            self._build_incidents_section(incidents),
            self._get_html_footer()
        ]
        
        return '\n'.join(html_parts)
    
    @staticmethod
    def format_date(date_str: str) -> str:
        """
        Format date from ISO to Colombian time (UTC-5).

        Args:
            date_str: Date in ISO format

        Returns:
            Formatted date string
        """
        if not date_str or date_str == "N/A":
            return "N/A"

        try:
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                time_part = date_str.split('T')[1].split('.')[0] if '.' in date_str else date_str.split('T')[1].split('+')[0]

                # Convert to datetime (assuming UTC)
                date_obj = datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")

                # Convert to Colombian time (UTC-5)
                colombia_time = date_obj - timedelta(hours=5)

                # Format as DD/MM/YYYY HH:MM (COL)
                return colombia_time.strftime("%d/%m/%Y %H:%M (COL)")
            else:
                return date_str
        except Exception:
            return date_str

    def _get_html_header(self) -> str:
        """Get HTML document header with styles."""
        return '''<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte BMC Remedy</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 20px; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .section-title { color: #2c3e50; border-left: 4px solid #3498db; padding-left: 15px; margin-bottom: 15px; }
        .user-info { background: #ecf0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary { display: flex; justify-content: space-around; margin-bottom: 20px; }
        .summary-item { text-align: center; background: #3498db; color: white; padding: 15px; border-radius: 5px; min-width: 120px; }
        .table-container { overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #34495e; color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #e8f4f8; }
        .status-open { color: #e74c3c; font-weight: bold; }
        .status-closed { color: #27ae60; font-weight: bold; }
        .priority-high { color: #e74c3c; font-weight: bold; }
        .priority-medium { color: #f39c12; font-weight: bold; }
        .priority-low { color: #27ae60; font-weight: bold; }
        .notes-section { margin-top: 10px; }
        .note-item { background: #f8f9fa; border-left: 3px solid #3498db; padding: 10px; margin: 5px 0; }
        .note-header { font-weight: bold; color: #2c3e50; margin-bottom: 5px; }
        .timestamp { color: #7f8c8d; font-size: 0.9em; }
        .no-data { text-align: center; color: #7f8c8d; font-style: italic; padding: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Reporte BMC Remedy</h1>
            <p>Consulta de Casos y Incidentes</p>
        </div>'''

    def _build_user_section(self, user_info: Dict[str, Any], search_params: Dict[str, Any]) -> str:
        """Build user information section."""
        cedula = search_params.get('cedula', 'N/A')
        timestamp = search_params.get('timestamp', 'N/A')

        return f'''
        <div class="section">
            <h2 class="section-title">👤 Información del Usuario</h2>
            <div class="user-info">
                <p><strong>Cédula:</strong> {cedula}</p>
                <p><strong>Nombre:</strong> {user_info.get('name', 'N/A')}</p>
                <p><strong>Email:</strong> {user_info.get('Internet E-mail', 'N/A')}</p>
                <p><strong>Teléfono:</strong> {user_info.get('Phone Number', 'N/A')}</p>
                <p><strong>Organización:</strong> {user_info.get('Organization', 'N/A')}</p>
                <p><strong>Fecha de consulta:</strong> {self.format_date(timestamp)}</p>
            </div>
        </div>'''

    def _build_summary_section(self, tickets_count: int, incidents_count: int) -> str:
        """Build summary section."""
        return f'''
        <div class="section">
            <h2 class="section-title">📊 Resumen</h2>
            <div class="summary">
                <div class="summary-item">
                    <h3>{tickets_count}</h3>
                    <p>Tickets</p>
                </div>
                <div class="summary-item">
                    <h3>{incidents_count}</h3>
                    <p>Incidentes</p>
                </div>
                <div class="summary-item">
                    <h3>{tickets_count + incidents_count}</h3>
                    <p>Total</p>
                </div>
            </div>
        </div>'''

    def _build_tickets_section(self, tickets: List[Dict[str, Any]]) -> str:
        """Build tickets section."""
        if not tickets:
            return '''
        <div class="section">
            <h2 class="section-title">🎫 Tickets</h2>
            <div class="no-data">No se encontraron tickets</div>
        </div>'''

        rows = []
        for ticket in tickets:
            status_class = "status-closed" if ticket.get('Status', '').lower() in ['closed', 'resolved'] else "status-open"
            priority_class = f"priority-{ticket.get('Priority', 'medium').lower()}"

            rows.append(f'''
                <tr>
                    <td>{ticket.get('Incident Number', 'N/A')}</td>
                    <td>{ticket.get('Description', 'N/A')}</td>
                    <td class="{status_class}">{ticket.get('Status', 'N/A')}</td>
                    <td class="{priority_class}">{ticket.get('Priority', 'N/A')}</td>
                    <td>{self.format_date(ticket.get('Submit Date', 'N/A'))}</td>
                    <td>{self.format_date(ticket.get('Last Modified Date', 'N/A'))}</td>
                </tr>''')

        return f'''
        <div class="section">
            <h2 class="section-title">🎫 Tickets ({len(tickets)})</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Descripción</th>
                            <th>Estado</th>
                            <th>Prioridad</th>
                            <th>Fecha Creación</th>
                            <th>Última Modificación</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join(rows)}
                    </tbody>
                </table>
            </div>
        </div>'''

    def _build_incidents_section(self, incidents: List[Dict[str, Any]]) -> str:
        """Build incidents section."""
        if not incidents:
            return '''
        <div class="section">
            <h2 class="section-title">🚨 Incidentes</h2>
            <div class="no-data">No se encontraron incidentes</div>
        </div>'''

        rows = []
        for incident in incidents:
            status_class = "status-closed" if incident.get('Status', '').lower() in ['closed', 'resolved'] else "status-open"
            priority_class = f"priority-{incident.get('Priority', 'medium').lower()}"

            rows.append(f'''
                <tr>
                    <td>{incident.get('Incident Number', 'N/A')}</td>
                    <td>{incident.get('Description', 'N/A')}</td>
                    <td class="{status_class}">{incident.get('Status', 'N/A')}</td>
                    <td class="{priority_class}">{incident.get('Priority', 'N/A')}</td>
                    <td>{self.format_date(incident.get('Submit Date', 'N/A'))}</td>
                    <td>{self.format_date(incident.get('Last Modified Date', 'N/A'))}</td>
                </tr>''')

        return f'''
        <div class="section">
            <h2 class="section-title">🚨 Incidentes ({len(incidents)})</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>Número</th>
                            <th>Descripción</th>
                            <th>Estado</th>
                            <th>Prioridad</th>
                            <th>Fecha Creación</th>
                            <th>Última Modificación</th>
                        </tr>
                    </thead>
                    <tbody>
                        {''.join(rows)}
                    </tbody>
                </table>
            </div>
        </div>'''

    def _get_html_footer(self) -> str:
        """Get HTML document footer."""
        return '''
    </div>
</body>
</html>'''
