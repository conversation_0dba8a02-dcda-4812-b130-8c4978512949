#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Utility functions for data formatting and display.
"""

from datetime import datetime, timedelta
from typing import Any, Dict, List
from prettytable import PrettyTable


def format_date_colombia(date_str: str) -> str:
    """
    Format date from ISO to Colombian time (UTC-5).
    
    Args:
        date_str: Date in ISO format
        
    Returns:
        Formatted date string
    """
    if not date_str or date_str == "N/A":
        return "N/A"

    try:
        if 'T' in date_str:
            date_part = date_str.split('T')[0]
            time_part = date_str.split('T')[1].split('.')[0] if '.' in date_str else date_str.split('T')[1].split('+')[0]
            
            # Convert to datetime (assuming UTC)
            date_obj = datetime.strptime(f"{date_part} {time_part}", "%Y-%m-%d %H:%M:%S")
            
            # Convert to Colombian time (UTC-5)
            colombia_time = date_obj - timedelta(hours=5)
            
            # Format as DD/MM/YYYY HH:MM (COL)
            return colombia_time.strftime("%d/%m/%Y %H:%M (COL)")
        else:
            return date_str
    except Exception:
        return date_str


def create_table_from_data(data: List[Dict[str, Any]], headers: List[str], 
                          title: str = "") -> PrettyTable:
    """
    Create a formatted table from data.
    
    Args:
        data: List of dictionaries with data
        headers: List of column headers
        title: Table title
        
    Returns:
        PrettyTable instance
    """
    table = PrettyTable()
    table.field_names = headers
    
    if title:
        table.title = title
    
    for item in data:
        row = []
        for header in headers:
            value = item.get(header, 'N/A')
            # Format dates if they look like ISO dates
            if isinstance(value, str) and 'T' in value and len(value) > 10:
                value = format_date_colombia(value)
            row.append(str(value)[:50])  # Truncate long values
        table.add_row(row)
    
    return table


def truncate_text(text: str, max_length: int = 50) -> str:
    """
    Truncate text to specified length.
    
    Args:
        text: Text to truncate
        max_length: Maximum length
        
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def format_status(status: str) -> str:
    """
    Format status with color coding for console output.
    
    Args:
        status: Status string
        
    Returns:
        Formatted status string
    """
    if not status:
        return "N/A"
    
    status_lower = status.lower()
    if status_lower in ['closed', 'resolved', 'completed']:
        return f"✅ {status}"
    elif status_lower in ['open', 'new', 'assigned']:
        return f"🔴 {status}"
    elif status_lower in ['in progress', 'pending']:
        return f"🟡 {status}"
    else:
        return f"⚪ {status}"


def format_priority(priority: str) -> str:
    """
    Format priority with visual indicators.
    
    Args:
        priority: Priority string
        
    Returns:
        Formatted priority string
    """
    if not priority:
        return "N/A"
    
    priority_lower = priority.lower()
    if priority_lower in ['high', 'critical', 'urgent']:
        return f"🔥 {priority}"
    elif priority_lower in ['medium', 'normal']:
        return f"🟡 {priority}"
    elif priority_lower in ['low']:
        return f"🟢 {priority}"
    else:
        return f"⚪ {priority}"
