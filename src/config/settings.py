#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Optimized configuration management for BMC Remedy Container.
Centralized settings with environment variable support.
"""

import os
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv


class Config:
    """Centralized configuration class."""
    
    def __init__(self, env_file: Optional[str] = None):
        """Initialize configuration."""
        self.env_file = env_file or ".env"
        self._load_environment()
        self._setup_defaults()
    
    def _load_environment(self):
        """Load environment variables from .env file if it exists."""
        env_path = Path(self.env_file)
        if env_path.exists():
            load_dotenv(env_path)
    
    def _setup_defaults(self):
        """Setup default configuration values."""
        # Application settings
        self.app_name = os.getenv("APP_NAME", "bmc-remedy")
        self.version = os.getenv("APP_VERSION", "2.0.0")
        self.debug = self._get_bool("DEBUG", False)
        self.silent_mode = self._get_bool("SILENT_MODE", False)
        self.max_incidents_display = self._get_int("MAX_INCIDENTS_DISPLAY", 5)
        
        # Logging settings
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.log_output = os.getenv("LOG_OUTPUT", "both")
        self.log_dir = os.getenv("LOG_DIR", "logs")
        self.enable_colors = self._get_bool("ENABLE_COLORS", True)
        
        # BMC settings
        self.bmc_api_base_url = os.getenv("API_BASE_URL", "")
        self.bmc_login_url = os.getenv("LOGIN_URL", "")
        self.bmc_username = os.getenv("USERNAME_", "")
        self.bmc_password = os.getenv("PASSWORD", "")
        self.bmc_timeout = self._get_int("BMC_TIMEOUT", 30)
        self.bmc_verify_ssl = self._get_bool("BMC_VERIFY_SSL", False)
        
        # Email settings
        self.email_url = os.getenv("EMAIL_URL", "")
        self.email_default_recipient = os.getenv("EMAIL_TO", "")
        self.email_timeout = self._get_int("EMAIL_TIMEOUT", 10)
        self.email_verify_ssl = self._get_bool("EMAIL_VERIFY_SSL", False)
    
    def _get_bool(self, key: str, default: bool) -> bool:
        """Get boolean value from environment."""
        return os.getenv(key, str(default)).lower() in ('true', '1', 'yes', 'on')
    
    def _get_int(self, key: str, default: int) -> int:
        """Get integer value from environment."""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    @property
    def bmc_config(self) -> Dict[str, Any]:
        """Get BMC configuration."""
        return {
            "api_base_url": self.bmc_api_base_url,
            "login_url": self.bmc_login_url,
            "username": self.bmc_username,
            "password": self.bmc_password,
            "timeout": self.bmc_timeout,
            "verify_ssl": self.bmc_verify_ssl
        }
    
    @property
    def email_config(self) -> Dict[str, Any]:
        """Get email configuration."""
        return {
            "mail_url": self.email_url,
            "default_recipient": self.email_default_recipient,
            "timeout": self.email_timeout,
            "verify_ssl": self.email_verify_ssl
        }
    
    @property
    def logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return {
            "level": self.log_level,
            "output_mode": self.log_output,
            "log_dir": self.log_dir,
            "enable_colors": self.enable_colors
        }


# Global configuration instance
_config = None


def get_config(env_file: Optional[str] = None) -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config(env_file)
    return _config


def setup_from_args(args) -> Config:
    """Setup configuration from command line arguments."""
    config = get_config()
    
    # Update configuration based on command line arguments
    if hasattr(args, 'debug') and args.debug:
        config.debug = True
        config.log_level = "DEBUG"
    
    if hasattr(args, 'silent') and args.silent:
        config.silent_mode = True
        config.log_output = "silent"
    
    if hasattr(args, 'verbose') and args.verbose:
        config.log_level = "DEBUG"
    
    return config


def reset_config():
    """Reset the global configuration (for testing)."""
    global _config
    _config = None
