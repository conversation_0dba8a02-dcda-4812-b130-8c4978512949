# BMC Remedy Configuration
# Replace these with your actual BMC Remedy server details
LOGIN_URL=https://your-bmc-server.com/api/jwt/login
API_BASE_URL=https://your-bmc-server.com/api
USERNAME_=your_username
PASSWORD=your_password

# Email Configuration
# Replace with your actual email service endpoint and recipient
EMAIL_URL=https://your-email-service.com/send
EMAIL_TO=<EMAIL>

# Application Settings
APP_NAME=bmc-remedy
APP_VERSION=2.0.0
DEBUG=false
SILENT_MODE=false
MAX_INCIDENTS_DISPLAY=5

# Logging Configuration
LOG_LEVEL=INFO
LOG_OUTPUT=both
LOG_DIR=logs
ENABLE_COLORS=true

# BMC Settings
BMC_TIMEOUT=30
BMC_VERIFY_SSL=false

# Email Settings
EMAIL_TIMEOUT=10
EMAIL_VERIFY_SSL=false
