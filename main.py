#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BMC Remedy Automation Tool - Optimized Version 2.0

Main entry point for the BMC Remedy automation system.
Provides a complete workflow for searching cases and sending reports.

Usage:
    python main.py <cedula> [--destinatario <email>] [--todos]
"""

import sys
import argparse
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from core.workflow import WorkflowManager
from config.settings import setup_from_args
from utils.logging import setup_logging, get_logger


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description='BMC Remedy Automation Tool - Search cases and send reports',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s 1152213619
  %(prog)s 1152213619 --destinatario <EMAIL>
  %(prog)s 1152213619 --todos
        """
    )

    # Main arguments
    parser.add_argument('cedula', help='ID number to search')
    parser.add_argument('--destinatario', '-d', help='Email recipient (optional)')
    parser.add_argument('--todos', '-t', action='store_true', help='Show all incidents')
    
    # Additional options
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--silent', action='store_true', help='Silent mode')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')

    args = parser.parse_args()

    try:
        # Setup configuration and logging
        config = setup_from_args(args)
        setup_logging(config)
        logger = get_logger('main')

        logger.info(f"Starting BMC Remedy Automation - cedula: {args.cedula}")

        # Initialize and run workflow
        workflow = WorkflowManager(config)
        success = workflow.run_complete_workflow(
            cedula=args.cedula,
            destinatario=args.destinatario,
            show_all=args.todos
        )

        if success:
            logger.info("Workflow completed successfully")
            sys.exit(0)
        else:
            logger.error("Workflow completed with errors")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\nProcess interrupted by user")
        sys.exit(1)
    except Exception as e:
        try:
            logger = get_logger('main')
            logger.error(f"Unexpected error: {str(e)}")
        except:
            print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
