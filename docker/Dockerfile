# Multi-stage build for optimized BMC Remedy Container
FROM python:3.11-slim as builder

# Set build arguments
ARG BUILD_DATE
ARG VERSION=2.0.0

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim

# Set labels
LABEL maintainer="BMC Remedy Team"
LABEL version="${VERSION}"
LABEL build-date="${BUILD_DATE}"
LABEL description="Optimized BMC Remedy Automation Container"

# Create non-root user
RUN groupadd -r bmcuser && useradd -r -g bmcuser bmcuser

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy application code
COPY . .

# Create necessary directories and set permissions
RUN mkdir -p /app/logs && \
    chown -R bmcuser:bmcuser /app && \
    chmod +x main.py

# Switch to non-root user
USER bmcuser

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app/src

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "from src.config.settings import get_config; print('OK')" || exit 1

# Expose port (for future web interface)
EXPOSE 8000

# Default command
CMD ["python", "main.py", "--help"]
