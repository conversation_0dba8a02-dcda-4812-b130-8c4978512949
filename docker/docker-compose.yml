version: '3.8'

services:
  # Main application service
  bmc-remedy:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      args:
        BUILD_DATE: ${BUILD_DATE:-$(date -u +'%Y-%m-%dT%H:%M:%SZ')}
        VERSION: ${VERSION:-2.0.0}
    container_name: bmc-remedy-optimized
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app/src
    volumes:
      # Persistent logs
      - ../logs:/app/logs
      # Configuration file
      - ../.env:/app/.env:ro
      # Optional: Mount custom config
      - ../config:/app/config:ro
    networks:
      - bmc-network
    # Default help command
    command: ["python", "main.py", "--help"]
    
  # Development service with code mounting
  bmc-remedy-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: builder
    container_name: bmc-remedy-dev
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app/src
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ../logs:/app/logs
      - ../.env:/app/.env:ro
      # Mount source code for development
      - ../src:/app/src
      - ../main.py:/app/main.py
      - ../requirements.txt:/app/requirements.txt
    networks:
      - bmc-network
    # Keep container running for development
    command: ["tail", "-f", "/dev/null"]
    profiles:
      - dev
    
  # Testing service
  bmc-remedy-test:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: bmc-remedy-test
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app/src
      - LOG_LEVEL=DEBUG
    volumes:
      - ../tests:/app/tests
      - ../src:/app/src
    networks:
      - bmc-network
    command: ["python", "-m", "pytest", "tests/", "-v"]
    profiles:
      - test

networks:
  bmc-network:
    driver: bridge
    name: bmc-remedy-network

volumes:
  bmc-logs:
    driver: local
