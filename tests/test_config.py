#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for configuration management.
"""

import os
import tempfile
import pytest
from pathlib import Path

# Add src to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.settings import Config, get_config, reset_config


class TestConfig:
    """Test configuration management."""
    
    def setup_method(self):
        """Setup for each test method."""
        reset_config()
    
    def teardown_method(self):
        """Cleanup after each test method."""
        reset_config()
    
    def test_default_config(self):
        """Test default configuration values."""
        config = Config()
        
        assert config.app_name == "bmc-remedy"
        assert config.version == "2.0.0"
        assert config.debug is False
        assert config.silent_mode is False
        assert config.max_incidents_display == 5
        assert config.log_level == "INFO"
        assert config.log_output == "both"
        assert config.enable_colors is True
    
    def test_env_file_loading(self):
        """Test loading configuration from .env file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("APP_NAME=test-app\n")
            f.write("DEBUG=true\n")
            f.write("LOG_LEVEL=DEBUG\n")
            f.write("MAX_INCIDENTS_DISPLAY=10\n")
            env_file = f.name
        
        try:
            config = Config(env_file=env_file)
            
            assert config.app_name == "test-app"
            assert config.debug is True
            assert config.log_level == "DEBUG"
            assert config.max_incidents_display == 10
        finally:
            os.unlink(env_file)
    
    def test_boolean_parsing(self):
        """Test boolean value parsing from environment."""
        test_cases = [
            ("true", True),
            ("True", True),
            ("TRUE", True),
            ("1", True),
            ("yes", True),
            ("on", True),
            ("false", False),
            ("False", False),
            ("FALSE", False),
            ("0", False),
            ("no", False),
            ("off", False),
            ("invalid", False)
        ]
        
        for value, expected in test_cases:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
                f.write(f"DEBUG={value}\n")
                env_file = f.name
            
            try:
                config = Config(env_file=env_file)
                assert config.debug == expected, f"Failed for value: {value}"
            finally:
                os.unlink(env_file)
    
    def test_integer_parsing(self):
        """Test integer value parsing from environment."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("MAX_INCIDENTS_DISPLAY=15\n")
            f.write("BMC_TIMEOUT=60\n")
            env_file = f.name
        
        try:
            config = Config(env_file=env_file)
            assert config.max_incidents_display == 15
            assert config.bmc_timeout == 60
        finally:
            os.unlink(env_file)
    
    def test_invalid_integer_fallback(self):
        """Test fallback to default for invalid integer values."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
            f.write("MAX_INCIDENTS_DISPLAY=invalid\n")
            env_file = f.name
        
        try:
            config = Config(env_file=env_file)
            assert config.max_incidents_display == 5  # Default value
        finally:
            os.unlink(env_file)
    
    def test_config_properties(self):
        """Test configuration property methods."""
        config = Config()
        
        # Test BMC config
        bmc_config = config.bmc_config
        assert isinstance(bmc_config, dict)
        assert "api_base_url" in bmc_config
        assert "login_url" in bmc_config
        assert "username" in bmc_config
        assert "password" in bmc_config
        assert "timeout" in bmc_config
        assert "verify_ssl" in bmc_config
        
        # Test email config
        email_config = config.email_config
        assert isinstance(email_config, dict)
        assert "mail_url" in email_config
        assert "default_recipient" in email_config
        assert "timeout" in email_config
        assert "verify_ssl" in email_config
        
        # Test logging config
        logging_config = config.logging_config
        assert isinstance(logging_config, dict)
        assert "level" in logging_config
        assert "output_mode" in logging_config
        assert "log_dir" in logging_config
        assert "enable_colors" in logging_config
    
    def test_global_config_singleton(self):
        """Test global configuration singleton behavior."""
        config1 = get_config()
        config2 = get_config()
        
        assert config1 is config2
        
        # Reset and test again
        reset_config()
        config3 = get_config()
        
        assert config1 is not config3


if __name__ == "__main__":
    pytest.main([__file__])
