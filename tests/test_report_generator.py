#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for report generator.
"""

import json
import tempfile
import pytest
from pathlib import Path

# Add src to path
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from core.report_generator import ReportGenerator
from config.settings import Config


class TestReportGenerator:
    """Test HTML report generation."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.config = Config()
        self.generator = ReportGenerator(self.config)
    
    def test_format_date(self):
        """Test date formatting."""
        # Test valid ISO date
        iso_date = "2025-02-16T01:29:50.000+0000"
        formatted = ReportGenerator.format_date(iso_date)
        assert "(COL)" in formatted
        assert "15/02/2025" in formatted  # Should be one day earlier due to UTC-5
        
        # Test invalid dates
        assert ReportGenerator.format_date("") == "N/A"
        assert ReportGenerator.format_date("N/A") == "N/A"
        assert ReportGenerator.format_date("invalid") == "invalid"
    
    def test_generate_html_report_missing_file(self):
        """Test HTML generation with missing file."""
        non_existent_file = Path("/non/existent/file.json")
        result = self.generator.generate_html_report(non_existent_file)
        assert result is None
    
    def test_generate_html_report_valid_data(self):
        """Test HTML generation with valid data."""
        # Create test data
        test_data = {
            "user_info": {
                "name": "John Doe",
                "Internet E-mail": "<EMAIL>",
                "Phone Number": "+57 ************",
                "Organization": "IT Department"
            },
            "tickets": {
                "data": [
                    {
                        "Incident Number": "INC000001234567",
                        "Description": "Test ticket",
                        "Status": "Open",
                        "Priority": "High",
                        "Submit Date": "2025-02-16T01:29:50.000+0000",
                        "Last Modified Date": "2025-02-16T02:15:30.000+0000"
                    }
                ],
                "count": 1
            },
            "incidents": {
                "data": [
                    {
                        "Incident Number": "INC000001234568",
                        "Description": "Test incident",
                        "Status": "Closed",
                        "Priority": "Medium",
                        "Submit Date": "2025-02-15T10:00:00.000+0000",
                        "Last Modified Date": "2025-02-15T11:30:00.000+0000"
                    }
                ],
                "count": 1
            },
            "search_params": {
                "cedula": "1152213619",
                "show_all": False,
                "timestamp": "2025-02-16T07:30:00.000000"
            }
        }
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = Path(f.name)
        
        try:
            # Generate HTML report
            html_content = self.generator.generate_html_report(temp_file)
            
            # Verify HTML content
            assert html_content is not None
            assert "<!DOCTYPE html>" in html_content
            assert "John Doe" in html_content
            assert "<EMAIL>" in html_content
            assert "INC000001234567" in html_content
            assert "INC000001234568" in html_content
            assert "Test ticket" in html_content
            assert "Test incident" in html_content
            assert "1152213619" in html_content
            
            # Check for Colombian time formatting
            assert "(COL)" in html_content
            
            # Check for status classes
            assert "status-open" in html_content
            assert "status-closed" in html_content
            
            # Check for priority classes
            assert "priority-high" in html_content
            assert "priority-medium" in html_content
            
        finally:
            temp_file.unlink()
    
    def test_generate_html_report_empty_data(self):
        """Test HTML generation with empty data."""
        test_data = {
            "user_info": {},
            "tickets": {"data": [], "count": 0},
            "incidents": {"data": [], "count": 0},
            "search_params": {"cedula": "1152213619"}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = Path(f.name)
        
        try:
            html_content = self.generator.generate_html_report(temp_file)
            
            assert html_content is not None
            assert "No se encontraron tickets" in html_content
            assert "No se encontraron incidentes" in html_content
            assert "1152213619" in html_content
            
        finally:
            temp_file.unlink()
    
    def test_generate_html_report_invalid_json(self):
        """Test HTML generation with invalid JSON."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            temp_file = Path(f.name)
        
        try:
            html_content = self.generator.generate_html_report(temp_file)
            assert html_content is None
            
        finally:
            temp_file.unlink()
    
    def test_html_structure(self):
        """Test HTML structure and styling."""
        test_data = {
            "user_info": {"name": "Test User"},
            "tickets": {"data": [], "count": 0},
            "incidents": {"data": [], "count": 0},
            "search_params": {"cedula": "123"}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = Path(f.name)
        
        try:
            html_content = self.generator.generate_html_report(temp_file)
            
            # Check HTML structure
            assert "<!DOCTYPE html>" in html_content
            assert "<html lang=\"es\">" in html_content
            assert "<head>" in html_content
            assert "<body>" in html_content
            assert "</html>" in html_content
            
            # Check CSS styles
            assert "<style>" in html_content
            assert "font-family: Arial" in html_content
            assert ".container" in html_content
            assert ".header" in html_content
            assert ".section" in html_content
            
            # Check responsive design
            assert "viewport" in html_content
            assert "max-width" in html_content
            
        finally:
            temp_file.unlink()


if __name__ == "__main__":
    pytest.main([__file__])
