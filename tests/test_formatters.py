#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Tests for formatting utilities.
"""

import pytest
from datetime import datetime

# Add src to path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.formatters import (
    format_date_colombia,
    create_table_from_data,
    truncate_text,
    format_status,
    format_priority
)


class TestFormatters:
    """Test formatting utility functions."""
    
    def test_format_date_colombia(self):
        """Test Colombian date formatting."""
        # Test valid ISO date
        iso_date = "2025-02-16T01:29:50.000+0000"
        formatted = format_date_colombia(iso_date)
        assert "(COL)" in formatted
        assert "/" in formatted
        
        # Test another format
        iso_date2 = "2025-02-16T06:30:00"
        formatted2 = format_date_colombia(iso_date2)
        assert "(COL)" in formatted2
        
        # Test invalid dates
        assert format_date_colombia("") == "N/A"
        assert format_date_colombia("N/A") == "N/A"
        assert format_date_colombia(None) == "N/A"
        assert format_date_colombia("invalid-date") == "invalid-date"
        
        # Test non-ISO format
        simple_date = "2025-02-16"
        assert format_date_colombia(simple_date) == simple_date
    
    def test_create_table_from_data(self):
        """Test table creation from data."""
        data = [
            {"Name": "John", "Status": "Active", "Date": "2025-02-16T01:29:50"},
            {"Name": "Jane", "Status": "Inactive", "Date": "2025-02-15T10:00:00"}
        ]
        headers = ["Name", "Status", "Date"]
        
        table = create_table_from_data(data, headers, "Test Table")
        
        assert table.title == "Test Table"
        assert table.field_names == headers
        assert len(table._rows) == 2
        
        # Test with empty data
        empty_table = create_table_from_data([], headers)
        assert len(empty_table._rows) == 0
    
    def test_truncate_text(self):
        """Test text truncation."""
        # Test normal truncation
        long_text = "This is a very long text that should be truncated"
        truncated = truncate_text(long_text, 20)
        assert len(truncated) <= 20
        assert truncated.endswith("...")
        
        # Test short text (no truncation needed)
        short_text = "Short"
        assert truncate_text(short_text, 20) == short_text
        
        # Test empty/None text
        assert truncate_text("", 20) == ""
        assert truncate_text(None, 20) is None
        
        # Test exact length
        exact_text = "Exactly twenty chars"
        assert truncate_text(exact_text, 20) == exact_text
    
    def test_format_status(self):
        """Test status formatting with emojis."""
        # Test closed statuses
        assert "✅" in format_status("Closed")
        assert "✅" in format_status("closed")
        assert "✅" in format_status("Resolved")
        assert "✅" in format_status("Completed")
        
        # Test open statuses
        assert "🔴" in format_status("Open")
        assert "🔴" in format_status("New")
        assert "🔴" in format_status("Assigned")
        
        # Test in progress statuses
        assert "🟡" in format_status("In Progress")
        assert "🟡" in format_status("Pending")
        
        # Test unknown status
        assert "⚪" in format_status("Unknown")
        
        # Test empty/None status
        assert format_status("") == "N/A"
        assert format_status(None) == "N/A"
    
    def test_format_priority(self):
        """Test priority formatting with emojis."""
        # Test high priority
        assert "🔥" in format_priority("High")
        assert "🔥" in format_priority("Critical")
        assert "🔥" in format_priority("Urgent")
        
        # Test medium priority
        assert "🟡" in format_priority("Medium")
        assert "🟡" in format_priority("Normal")
        
        # Test low priority
        assert "🟢" in format_priority("Low")
        
        # Test unknown priority
        assert "⚪" in format_priority("Unknown")
        
        # Test empty/None priority
        assert format_priority("") == "N/A"
        assert format_priority(None) == "N/A"
    
    def test_date_formatting_edge_cases(self):
        """Test edge cases for date formatting."""
        # Test with milliseconds
        date_with_ms = "2025-02-16T01:29:50.123+0000"
        formatted = format_date_colombia(date_with_ms)
        assert "(COL)" in formatted
        
        # Test without timezone
        date_no_tz = "2025-02-16T01:29:50"
        formatted = format_date_colombia(date_no_tz)
        assert "(COL)" in formatted
        
        # Test malformed date
        malformed = "2025-02-16T25:70:80"
        assert format_date_colombia(malformed) == malformed
    
    def test_table_data_truncation(self):
        """Test that table data is properly truncated."""
        long_description = "A" * 100  # 100 character string
        data = [{"Description": long_description}]
        headers = ["Description"]
        
        table = create_table_from_data(data, headers)
        
        # Check that the data was truncated to 50 characters
        row_data = table._rows[0]
        assert len(row_data[0]) <= 50


if __name__ == "__main__":
    pytest.main([__file__])
