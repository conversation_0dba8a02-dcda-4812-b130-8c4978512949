#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Diagnostic Script for BMC Remedy Automation Tool

This script helps diagnose common issues with the BMC Remedy automation tool.
"""

import os
import sys
from pathlib import Path
import requests
from urllib.parse import urlparse

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))


def check_file_structure():
    """Check if all required files exist."""
    print("📁 Checking file structure...")
    
    required_files = [
        'main.py',
        'src/core/workflow.py',
        'src/core/bmc_client.py',
        'src/core/report_generator.py',
        'src/core/email_sender.py',
        'src/config/settings.py',
        'src/utils/logging.py',
        'src/utils/formatters.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✅ All required files present")
        return True


def check_configuration():
    """Check configuration setup."""
    print("\n⚙️ Checking configuration...")
    
    if not Path('.env').exists():
        print("❌ No .env file found")
        print("💡 Run: python setup_config.py")
        return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv('.env')
    
    required_vars = {
        'LOGIN_URL': 'BMC Login URL',
        'API_BASE_URL': 'BMC API Base URL', 
        'USERNAME_': 'BMC Username',
        'PASSWORD': 'BMC Password',
        'EMAIL_URL': 'Email Service URL',
        'EMAIL_TO': 'Default Email Recipient'
    }
    
    issues = []
    for var, description in required_vars.items():
        value = os.getenv(var, '').strip()
        if not value:
            issues.append(f"Missing {description} ({var})")
        elif var in ['LOGIN_URL', 'API_BASE_URL', 'EMAIL_URL']:
            if not value.startswith(('http://', 'https://')):
                issues.append(f"Invalid URL format for {description}: {value}")
    
    if issues:
        print("❌ Configuration issues:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ Configuration looks good")
        return True


def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'requests',
        'python-dotenv',
        'pytz'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    else:
        print("✅ All required packages installed")
        return True


def test_connectivity():
    """Test connectivity to BMC and email services."""
    print("\n🌐 Testing connectivity...")
    
    if not Path('.env').exists():
        print("❌ No .env file found - skipping connectivity tests")
        return False
    
    from dotenv import load_dotenv
    load_dotenv('.env')
    
    login_url = os.getenv('LOGIN_URL', '').strip()
    email_url = os.getenv('EMAIL_URL', '').strip()
    
    results = []
    
    # Test BMC connectivity
    if login_url:
        try:
            parsed_url = urlparse(login_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            response = requests.get(base_url, timeout=10, verify=False)
            if response.status_code < 500:
                print(f"✅ BMC server reachable: {base_url}")
                results.append(True)
            else:
                print(f"⚠️  BMC server responded with status {response.status_code}: {base_url}")
                results.append(False)
        except Exception as e:
            print(f"❌ Cannot reach BMC server: {str(e)}")
            results.append(False)
    
    # Test email service connectivity
    if email_url:
        try:
            parsed_url = urlparse(email_url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            response = requests.get(base_url, timeout=10, verify=False)
            if response.status_code < 500:
                print(f"✅ Email service reachable: {base_url}")
                results.append(True)
            else:
                print(f"⚠️  Email service responded with status {response.status_code}: {base_url}")
                results.append(False)
        except Exception as e:
            print(f"❌ Cannot reach email service: {str(e)}")
            results.append(False)
    
    return all(results) if results else False


def check_logs():
    """Check recent log entries."""
    print("\n📋 Checking recent logs...")
    
    log_file = Path('logs/bmc-remedy.log')
    if not log_file.exists():
        print("ℹ️  No log file found yet")
        return True
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        if not lines:
            print("ℹ️  Log file is empty")
            return True
        
        # Show last 10 lines
        recent_lines = lines[-10:]
        print("Recent log entries:")
        for line in recent_lines:
            line = line.strip()
            if 'ERROR' in line:
                print(f"❌ {line}")
            elif 'WARNING' in line:
                print(f"⚠️  {line}")
            else:
                print(f"ℹ️  {line}")
        
        # Check for common errors
        error_count = sum(1 for line in recent_lines if 'ERROR' in line)
        if error_count > 0:
            print(f"\n⚠️  Found {error_count} recent errors")
            return False
        
        return True
    except Exception as e:
        print(f"❌ Error reading log file: {e}")
        return False


def main():
    """Main diagnostic function."""
    print("🔍 BMC Remedy Automation Tool - Diagnostic Report")
    print("=" * 60)
    
    checks = [
        ("File Structure", check_file_structure),
        ("Configuration", check_configuration),
        ("Dependencies", check_dependencies),
        ("Connectivity", test_connectivity),
        ("Logs", check_logs)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error during {check_name} check: {e}")
            results.append((check_name, False))
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{check_name:.<20} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All checks passed! Your BMC Remedy tool should work correctly.")
        print("\n💡 Try running: python main.py <cedula> --debug")
    else:
        print("⚠️  Some issues found. Please address the failed checks above.")
        print("\n💡 Common solutions:")
        print("  - Run: python setup_config.py (for configuration issues)")
        print("  - Run: pip install -r requirements.txt (for dependency issues)")
        print("  - Check your network connection and server URLs")


if __name__ == "__main__":
    main()
